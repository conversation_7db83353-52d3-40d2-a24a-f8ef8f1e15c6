#!/usr/bin/env python3
"""
脚本用于对比两个CSV文件中相同content_id对应的cover_static字段的差异
- tests/gouzi_homepage_cover_0904_v15.csv (bizid -> content_id, aftsid -> cover_static)
- tests/3_top2k_with_results.csv (content_id, cover_static)
"""

import pandas as pd
import os

def compare_cover_static():
    """对比两个CSV文件中相同content_id的cover_static字段差异"""
    
    # 文件路径
    file1_path = "tests/gouzi_homepage_cover_0904_v15.csv"
    file2_path = "tests/3_top2k_with_results.csv"
    
    # 检查文件是否存在
    if not os.path.exists(file1_path):
        print(f"错误：文件 {file1_path} 不存在")
        return
    
    if not os.path.exists(file2_path):
        print(f"错误：文件 {file2_path} 不存在")
        return
    
    print("正在读取CSV文件...")
    
    # 读取第一个文件 (gouzi_homepage_cover_0904_v15.csv)
    try:
        df1 = pd.read_csv(file1_path)
        print(f"文件1 ({file1_path}) 读取成功，共 {len(df1)} 行")
        print(f"文件1 列名: {list(df1.columns)}")
    except Exception as e:
        print(f"读取文件1失败: {e}")
        return
    
    # 读取第二个文件 (3_top2k_with_results.csv)
    try:
        df2 = pd.read_csv(file2_path)
        print(f"文件2 ({file2_path}) 读取成功，共 {len(df2)} 行")
        print(f"文件2 列名: {list(df2.columns)}")
    except Exception as e:
        print(f"读取文件2失败: {e}")
        return
    
    # 重命名第一个文件的列名以便对比
    # bizid -> content_id, aftsid -> cover_static
    df1_renamed = df1.rename(columns={'bizid': 'content_id', 'aftsid': 'cover_static'})
    
    # 只保留需要的列
    df1_compare = df1_renamed[['content_id', 'cover_static']].copy()
    df2_compare = df2[['content_id', 'cover_static']].copy()
    
    print(f"\n处理后的数据:")
    print(f"文件1 (gouzi): {len(df1_compare)} 条记录")
    print(f"文件2 (top2k): {len(df2_compare)} 条记录")
    
    # 找到共同的content_id
    common_content_ids = set(df1_compare['content_id']) & set(df2_compare['content_id'])
    print(f"\n共同的content_id数量: {len(common_content_ids)}")
    
    if len(common_content_ids) == 0:
        print("没有找到共同的content_id，无法进行对比")
        return
    
    # 对于共同的content_id，对比cover_static字段
    differences = []
    same_count = 0
    
    for content_id in common_content_ids:
        # 获取两个文件中对应的cover_static值
        cover1 = df1_compare[df1_compare['content_id'] == content_id]['cover_static'].iloc[0]
        cover2 = df2_compare[df2_compare['content_id'] == content_id]['cover_static'].iloc[0]
        
        if cover1 != cover2:
            differences.append({
                'content_id': content_id,
                'gouzi_cover_static': cover1,
                'top2k_cover_static': cover2
            })
        else:
            same_count += 1
    
    # 统计结果
    total_compared = len(common_content_ids)
    different_count = len(differences)
    
    print(f"\n=== 对比结果统计 ===")
    print(f"总共对比的content_id数量: {total_compared}")
    print(f"cover_static相同的数量: {same_count}")
    print(f"cover_static不同的数量: {different_count}")
    print(f"差异比例: {different_count/total_compared*100:.2f}%")
    
    # 显示前10个差异示例
    if differences:
        print(f"\n=== 前10个差异示例 ===")
        for i, diff in enumerate(differences[:10]):
            print(f"{i+1}. content_id: {diff['content_id']}")
            print(f"   gouzi文件: {diff['gouzi_cover_static']}")
            print(f"   top2k文件: {diff['top2k_cover_static']}")
            print()
    
    # 保存详细差异到文件
    if differences:
        diff_df = pd.DataFrame(differences)
        output_file = "cover_static_differences.csv"
        diff_df.to_csv(output_file, index=False)
        print(f"详细差异已保存到: {output_file}")
    
    return {
        'total_compared': total_compared,
        'same_count': same_count,
        'different_count': different_count,
        'differences': differences
    }

if __name__ == "__main__":
    print("开始对比cover_static字段差异...")
    result = compare_cover_static()
    print("对比完成！")
