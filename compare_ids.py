
import pandas as pd
import os

def compare_ids():
    # 获取当前脚本所在目录的父目录作为基础路径
    base_dir = os.path.dirname(os.path.abspath(__file__))

    # 读取第一个CSV文件，bizid列作为ID
    file1_path = os.path.join(base_dir, 'tests/gouzi_homepage_cover_0904_v15.csv')
    df1 = pd.read_csv(file1_path)
    ids1 = set(df1['bizid'].tolist())

    # 读取第二个CSV文件，content_id列作为ID
    # file2_path = os.path.join(base_dir, 'tests/3_top2k_with_results.csv')
    file2_path = os.path.join(base_dir, 'tests/gouzi_tmp_hot2k_0908.csv')
    df2 = pd.read_csv(file2_path)
    ids2 = set(df2['content_id'].tolist())

    # 计算相同和不同的ID数量
    common_ids = ids1.intersection(ids2)
    unique_to_file1 = ids1 - ids2
    unique_to_file2 = ids2 - ids1

    # 输出统计结果
    print(f"文件1 ({file1_path}) 中的ID总数: {len(ids1)}")
    print(f"文件2 ({file2_path}) 中的ID总数: {len(ids2)}")
    print(f"两个文件中相同的ID数量: {len(common_ids)}")
    print(f"仅在文件1中的ID数量: {len(unique_to_file1)}")
    print(f"仅在文件2中的ID数量: {len(unique_to_file2)}")

    # 可以选择输出一些示例ID
    print("\n示例 - 相同的ID:")
    for i, id_val in enumerate(list(common_ids)[:5]):
        print(f"  {i+1}. {id_val}")

    print("\n示例 - 仅在文件1中的ID:")
    for i, id_val in enumerate(list(unique_to_file1)[:5]):
        print(f"  {i+1}. {id_val}")

    print("\n示例 - 仅在文件2中的ID:")
    for i, id_val in enumerate(list(unique_to_file2)[:5]):
        print(f"  {i+1}. {id_val}")

if __name__ == "__main__":
    compare_ids()
