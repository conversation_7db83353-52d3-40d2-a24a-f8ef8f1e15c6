#!/usr/bin/env python3
"""
脚本用于合并 gouzi_tmp_hot2k_0908.csv 和 attraction_cover_result_0910.csv
生成新的表格 hot2k_attraction_cover_0910.csv
"""

import pandas as pd
import os

def create_hot2k_attraction_cover():
    """
    创建合并后的表格
    """
    # 读取源文件
    hot2k_file = "tests/gouzi_tmp_hot2k_0908.csv"
    attraction_file = "tests/attraction_cover_result_0910.csv"
    output_file = "tests/hot2k_attraction_cover_0910.csv"
    
    # 检查文件是否存在
    if not os.path.exists(hot2k_file):
        print(f"错误：文件 {hot2k_file} 不存在")
        return
    
    if not os.path.exists(attraction_file):
        print(f"错误：文件 {attraction_file} 不存在")
        return
    
    print("正在读取源文件...")
    
    # 读取 hot2k 文件
    try:
        hot2k_df = pd.read_csv(hot2k_file)
        print(f"成功读取 {hot2k_file}，共 {len(hot2k_df)} 行数据")
    except Exception as e:
        print(f"读取 {hot2k_file} 失败：{e}")
        return
    
    # 读取 attraction 文件
    try:
        attraction_df = pd.read_csv(attraction_file)
        print(f"成功读取 {attraction_file}，共 {len(attraction_df)} 行数据")
    except Exception as e:
        print(f"读取 {attraction_file} 失败：{e}")
        return
    
    # 创建结果数据框
    result_data = []
    
    print("正在处理数据...")
    
    # 遍历 hot2k 数据
    for _, row in hot2k_df.iterrows():
        content_id = row['content_id']
        scene_cover = row['scene_cover']
        
        # 检查是否在 attraction 结果中
        attraction_match = attraction_df[attraction_df['ctt_id'] == content_id]
        
        if not attraction_match.empty:
            # 在 attraction 结果中找到，使用新封面
            afts_id = attraction_match.iloc[0]['afts_id']
            cover_source = "new"
        else:
            # 未在 attraction 结果中找到，使用原封面
            afts_id = scene_cover
            cover_source = "src"
        
        # 添加到结果数据
        result_data.append({
            'bizId': content_id,
            'aftsId': afts_id,
            'subVersion': 21,
            'bizType': 'home_page_aigc',
            'dt': '0910',
            'coverSource': cover_source
        })
    
    # 创建结果数据框
    result_df = pd.DataFrame(result_data)
    
    # 保存结果
    try:
        result_df.to_csv(output_file, index=False)
        print(f"成功生成文件：{output_file}")
        print(f"总共处理了 {len(result_df)} 条记录")
        
        # 统计信息
        new_count = len(result_df[result_df['coverSource'] == 'new'])
        src_count = len(result_df[result_df['coverSource'] == 'src'])
        
        print(f"使用新封面的记录：{new_count} 条")
        print(f"使用原封面的记录：{src_count} 条")
        
        # 显示前几行结果
        print("\n前5行结果预览：")
        print(result_df.head().to_string(index=False))
        
    except Exception as e:
        print(f"保存文件失败：{e}")

if __name__ == "__main__":
    create_hot2k_attraction_cover()
